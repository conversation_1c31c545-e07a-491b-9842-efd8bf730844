<?php

namespace App\Console\Commands;

use App\Category;
use App\Product;
use App\Vendor;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Exceptions\UnreachableUrl;

class ImportSchoolSupplies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:school-supplies 
                            {--file=school supplies.csv : CSV file name in storage directory}
                            {--chunk=50 : Number of rows to process at once}
                            {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import school supplies products from CSV file';

    /**
     * Statistics tracking
     */
    private int $processedRows = 0;
    private int $createdProducts = 0;
    private int $addedImages = 0;
    private int $skippedRows = 0;
    private int $errorRows = 0;
    private ?Product $lastCreatedProduct = null;

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $fileName = $this->option('file');
        $chunkSize = (int) $this->option('chunk');
        $isDryRun = $this->option('dry-run');

        $filePath = storage_path($fileName);

        // Validate file exists
        if (!file_exists($filePath)) {
            $this->error("CSV file not found: {$filePath}");
            return 1;
        }

        // Validate category exists
        $category = Category::find(394);
        if (!$category) {
            $this->error('School Supplies category (ID: 394) not found');
            return 1;
        }

        // Validate vendor exists
        $vendor = Vendor::find(1579);
        if (!$vendor) {
            $this->error('A1 School Supplies vendor (ID: 1579) not found');
            return 1;
        }

        $this->info("Starting import from: {$fileName}");
        $this->info("Category: {$category->name} (ID: {$category->id})");
        $this->info("Vendor: {$vendor->name} (ID: {$vendor->id})");

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        try {
            $this->processCSVFile($filePath, $chunkSize, $isDryRun, $category);
            $this->displaySummary();
            return 0;
        } catch (\Exception $e) {
            $this->error("Import failed: {$e->getMessage()}");
            Log::error('School supplies import failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Process the CSV file in chunks
     */
    private function processCSVFile(string $filePath, int $chunkSize, bool $isDryRun, Category $category): void
    {
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            throw new \Exception('Could not open CSV file');
        }

        // Skip header row
        $header = fgetcsv($handle);
        $this->info('Header: ' . implode(' | ', $header));

        $rows = [];
        $rowNumber = 1; // Start at 1 since we skipped header

        while (($row = fgetcsv($handle)) !== false) {
            $rowNumber++;
            $rows[] = ['row' => $row, 'number' => $rowNumber];

            if (count($rows) >= $chunkSize) {
                $this->processChunk($rows, $isDryRun, $category);
                $rows = [];
            }
        }

        // Process remaining rows
        if (!empty($rows)) {
            $this->processChunk($rows, $isDryRun, $category);
        }

        fclose($handle);
    }

    /**
     * Process a chunk of CSV rows
     */
    private function processChunk(array $rows, bool $isDryRun, Category $category): void
    {
        foreach ($rows as $rowData) {
            $row = $rowData['row'];
            $rowNumber = $rowData['number'];

            try {
                $this->processRow($row, $rowNumber, $isDryRun, $category);
            } catch (\Exception $e) {
                $this->error("Error processing row {$rowNumber}: {$e->getMessage()}");
                Log::error('Error processing CSV row', [
                    'row_number' => $rowNumber,
                    'row_data' => $row,
                    'error' => $e->getMessage()
                ]);
                $this->errorRows++;
            }

            $this->processedRows++;
        }

        // Show progress
        $this->info("Processed {$this->processedRows} rows. Created: {$this->createdProducts} products, Added: {$this->addedImages} images");
    }

    /**
     * Process a single CSV row
     */
    private function processRow(array $row, int $rowNumber, bool $isDryRun, Category $category): void
    {
        // Ensure we have enough columns
        if (count($row) < 15) {
            $this->warn("Row {$rowNumber}: Insufficient columns (" . count($row) . "/15)");
            $this->skippedRows++;
            return;
        }

        $title = trim($row[0] ?? '');
        $imageUrl = trim($row[12] ?? '');

        // Check if this is an additional image row (empty title)
        if (empty($title) && !empty($imageUrl)) {
            $this->processAdditionalImage($imageUrl, $rowNumber, $isDryRun);
            return;
        }

        // Skip rows with empty title and no image
        if (empty($title)) {
            $this->skippedRows++;
            return;
        }

        // Create new product
        $this->createProduct($row, $rowNumber, $isDryRun, $category);
    }

    /**
     * Create a new product from CSV row
     */
    private function createProduct(array $row, int $rowNumber, bool $isDryRun, Category $category): void
    {
        $productData = $this->mapRowToProductData($row);

        if ($isDryRun) {
            $this->info("Would create product: {$productData['title']}");
            $this->info("Quantity: {$productData['store_quantity']}");
            $this->createdProducts++;
            return;
        }

        DB::transaction(function () use ($productData, $category, $row, $rowNumber) {
            // Create product
            $product = Product::create($productData);
            
            // Attach to School Supplies category
            $product->categories()->attach($category->id);
            
            // Add main image if provided
            $imageUrl = trim($row[12] ?? '');
            if (!empty($imageUrl)) {
                $this->addImageToProduct($product, $imageUrl, $rowNumber);
            }
            
            $this->lastCreatedProduct = $product;
            $this->createdProducts++;
            
            Log::info('Created product', [
                'id' => $product->id,
                'title' => $product->title,
                'sku' => $product->sku
            ]);
        });
    }

    /**
     * Map CSV row to product data array
     */
    private function mapRowToProductData(array $row): array
    {
        $title = trim($row[0]);
        $description = trim($row[1] ?? '');
        $published = $this->parseBooleanValue($row[5] ?? '');
        $sku = trim($row[6] ?? '');
        $weightGrams = (float) ($row[7] ?? 0);
        $weightOunces = $weightGrams > 0 ? round($weightGrams / 28.3495, 2) : null;
        $quantity = $row[8];//(int) ($row[8] ?? 0);
        $storePrice = (float) ($row[9] ?? 0);
        $listPrice = (float) ($row[10] ?? 0);
        $barcode = trim($row[11] ?? '');
        $costPrice = (float) ($row[13] ?? 0);

        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'description' => $description,
            'visibility' => $published,
            'sku' => $sku ?: null,
            'weight' => $weightOunces,
            'website_quantity' => $quantity,
            'track_inventory' => true,
            'online_price' => $storePrice > 0 ? $storePrice : null,
            'list_price' => $listPrice > 0 ? $listPrice : null,
            'barcode' => $barcode ?: null,
            'cost_price' => $costPrice > 0 ? $costPrice : null,
            'item_type' => 'physical',
            'vendor_id' => 1579, // A1 School Supplies vendor
        ];
    }

    /**
     * Process additional image for the last created product
     */
    private function processAdditionalImage(string $imageUrl, int $rowNumber, bool $isDryRun): void
    {
        if (!$this->lastCreatedProduct) {
            $this->warn("Row {$rowNumber}: Additional image found but no previous product to attach to");
            $this->skippedRows++;
            return;
        }

        if ($isDryRun) {
            $this->info("Would add additional image to product: {$this->lastCreatedProduct->title}");
            $this->addedImages++;
            return;
        }

        $this->addImageToProduct($this->lastCreatedProduct, $imageUrl, $rowNumber);
    }

    /**
     * Add image to product using Media Library
     */
    private function addImageToProduct(Product $product, string $imageUrl, int $rowNumber): void
    {
        if (!$this->isValidImageUrl($imageUrl)) {
            $this->warn("Row {$rowNumber}: Invalid image URL: {$imageUrl}");
            return;
        }

        try {
            $product->addMediaFromUrl($imageUrl)
                ->toMediaCollection('media');
            
            $this->addedImages++;
            
            Log::info('Added image to product', [
                'product_id' => $product->id,
                'image_url' => $imageUrl
            ]);
        } catch (UnreachableUrl $e) {
            $this->warn("Row {$rowNumber}: Could not download image: {$imageUrl}");
            Log::warning('Failed to download image', [
                'product_id' => $product->id,
                'image_url' => $imageUrl,
                'error' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            $this->error("Row {$rowNumber}: Error adding image: {$e->getMessage()}");
            Log::error('Error adding image to product', [
                'product_id' => $product->id,
                'image_url' => $imageUrl,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Validate image URL
     */
    private function isValidImageUrl(string $url): bool
    {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }

        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
        
        return in_array($extension, $allowedExtensions);
    }

    /**
     * Parse boolean value from CSV
     */
    private function parseBooleanValue(string $value): bool
    {
        $value = strtolower(trim($value));
        return in_array($value, ['1', 'true', 'yes', 'on']);
    }

    /**
     * Display import summary
     */
    private function displaySummary(): void
    {
        $this->info('');
        $this->info('=== Import Summary ===');
        $this->info("Total rows processed: {$this->processedRows}");
        $this->info("Products created: {$this->createdProducts}");
        $this->info("Images added: {$this->addedImages}");
        $this->info("Rows skipped: {$this->skippedRows}");
        $this->info("Rows with errors: {$this->errorRows}");
        $this->info('======================');
    }
}
